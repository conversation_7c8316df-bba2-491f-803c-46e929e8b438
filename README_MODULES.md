# Layout Bricks Instructions Analyzer - Modular Structure

This document describes the new modular structure of the Layout Bricks Instructions Analyzer, which has been refactored from a single `analyze.py` file into multiple focused modules.

## Module Structure

### 1. `models.py` - Data Models
Contains Pydantic models for structured data validation:
- `AnalysisResult`: Model for AI response structure with score, reasoning, keywords, and confidence

### 2. `rate_limiter.py` - Rate Limiting
Handles API rate limiting functionality:
- `RateLimiter`: Ensures API calls don't exceed rate limits with async support

### 3. `file_utils.py` - File Operations
Provides file discovery and processing utilities:
- `find_txt_files()`: Recursively finds .txt files in directories
- `load_processed_files()`: Loads list of already processed files from CSV
- `save_reasoning_file()`: Saves detailed analysis reasoning to individual files

### 4. `csv_handler.py` - CSV Operations
Manages CSV file operations:
- `save_to_csv()`: Saves analysis results to CSV with proper formatting and headers

### 5. `prompt_manager.py` - Prompt Management
Handles prompt template loading and formatting:
- `PromptManager`: Class for loading and formatting analysis prompts from external files

### 6. `config.py` - Configuration Management
Manages application configuration and environment setup:
- `setup_logging()`: Configures application logging
- `get_api_key()`: Retrieves API key from environment variables
- `Config`: Application configuration class with default values

### 7. `analyzer.py` - Core Analysis Logic
Contains the main analysis functionality:
- `LayoutBricksAnalyzer`: Main class that orchestrates file analysis using LiteLLM
- Handles async file processing, worker management, and result aggregation

### 8. `main.py` - Entry Point
Main application entry point:
- Command-line argument parsing
- Application initialization and execution
- Results summary and statistics display

### 9. `analyze.py` - Backward Compatibility
Maintains backward compatibility with the original script:
- Imports from new modular structure
- Provides same interface as original script

## Usage

### New Modular Approach (Recommended)
```bash
python main.py --input_folder input_ride --output-folder output_ride
```

### Backward Compatibility
```bash
python analyze.py --input_folder input_ride --output-folder output_ride
```

## Benefits of Modular Structure

1. **Separation of Concerns**: Each module has a single, well-defined responsibility
2. **Testability**: Individual components can be tested in isolation
3. **Reusability**: Modules can be imported and used in other projects
4. **Maintainability**: Easier to locate and modify specific functionality
5. **Scalability**: New features can be added without affecting existing modules
6. **Code Organization**: Logical grouping of related functionality

## Dependencies

The modular structure maintains the same dependencies as the original:
- `litellm`: For AI model integration
- `pydantic`: For data validation
- `python-dotenv`: For environment variable management (optional)
- Standard library modules: `asyncio`, `pathlib`, `csv`, `json`, `logging`, `argparse`

## Configuration

The application can be configured through:
- Environment variables (e.g., `OPENROUTER_API_KEY`)
- Command-line arguments
- External prompt template files
- The `Config` class in `config.py`

## Migration Guide

If you were using the original `analyze.py`:
1. No changes needed - backward compatibility is maintained
2. For new projects, use `main.py` instead
3. Individual modules can be imported as needed: `from analyzer import LayoutBricksAnalyzer`

## File Structure
```
├── analyze.py              # Backward compatibility wrapper
├── main.py                 # New main entry point
├── models.py               # Data models
├── rate_limiter.py         # Rate limiting functionality
├── file_utils.py           # File operations
├── csv_handler.py          # CSV handling
├── prompt_manager.py       # Prompt management
├── config.py               # Configuration management
├── analyzer.py             # Core analysis logic
└── README_MODULES.md       # This documentation
```
