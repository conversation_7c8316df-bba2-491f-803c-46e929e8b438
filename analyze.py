#!/usr/bin/env python3
"""
Layout Bricks Instructions Analyzer - Backward Compatibility Wrapper

This script maintains backward compatibility with the original analyze.py
while using the new modular structure.

For new projects, use main.py instead.

Requirements:
- litellm
- openrouter API key
- asyncio (built-in)

Usage:
    python analyze.py <input_folder> [output_folder]
"""

# Import from the new modular structure
from main import main
from config import setup_logging
from analyzer import LayoutBricksAnalyzer
from models import AnalysisResult
from rate_limiter import RateLimiter

# Set up logging
logger = setup_logging()

# For backward compatibility, expose the classes from the new modules
__all__ = ['AnalysisResult', 'RateLimiter', 'LayoutBricksAnalyzer', 'main']


if __name__ == "__main__":
    # Run the main function from the new main.py module
    exit(main())
