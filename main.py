#!/usr/bin/env python3
"""
Layout Bricks Instructions Analyzer - Main Entry Point

This script analyzes all .txt files in a folder and its subfolders to detect
"layout bricks instructions" using LiteLLM with OpenRouter's DeepSeek model.
Now supports parallel processing with rate limiting and interactive user input loop.

Requirements:
- litellm
- openrouter API key
- asyncio (built-in)
- requests (for API call checking)

Usage:
    python main.py <input_folder> [output_folder]
"""

import argparse
import logging
import requests
import time
import json

from config import setup_logging, get_api_key
from analyzer import LayoutBricksAnalyzer

logger = setup_logging()


def check_api_calls_remaining(api_key: str = None) -> dict:
    """Check remaining free API calls on OpenRouter."""
    try:
        if not api_key:
            api_key = get_api_key()

        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }

        response = requests.get('https://openrouter.ai/api/v1/auth/key', headers=headers)
        response.raise_for_status()

        data = response.json()
        return {
            'success': True,
            'data': data,
            'remaining_calls': data.get('usage', {}).get('remaining', 'Unknown')
        }
    except Exception as e:
        logger.error(f"Failed to check API calls: {e}")
        return {
            'success': False,
            'error': str(e),
            'remaining_calls': 'Unknown'
        }


def interactive_analysis_loop():
    """Interactive loop for user-driven analysis."""
    print("\n" + "="*60)
    print("INTERACTIVE ANALYSIS MODE")
    print("="*60)
    print("Commands:")
    print("  'analyze' - Run analysis on configured folders")
    print("  'check' - Check remaining API calls")
    print("  'status' - Show current configuration")
    print("  'stop' - Exit the program")
    print("="*60)

    # Default configuration
    config = {
        'input_folder': 'input_ride',
        'output_folder': 'output_ride',
        'prompt_file': '_prompt.txt',
        'max_workers': 4,
        'max_calls_per_minute': 18,
        'api_key': None
    }

    try:
        config['api_key'] = get_api_key()
    except ValueError as e:
        print(f"Warning: {e}")
        return 1

    while True:
        try:
            user_input = input("\nEnter command: ").strip().lower()

            if user_input == 'stop':
                print("Exiting program...")
                break
            elif user_input == 'check':
                print("Checking remaining API calls...")
                result = check_api_calls_remaining(config['api_key'])
                if result['success']:
                    print(f"Remaining API calls: {result['remaining_calls']}")
                    if isinstance(result['data'], dict):
                        print(f"Full API response: {json.dumps(result['data'], indent=2)}")
                else:
                    print(f"Failed to check API calls: {result['error']}")
                time.sleep(5)  # Sleep for 5 seconds as requested
            elif user_input == 'status':
                print(f"Current configuration:")
                print(f"  Input folder: {config['input_folder']}")
                print(f"  Output folder: {config['output_folder']}")
                print(f"  Prompt file: {config['prompt_file']}")
                print(f"  Max workers: {config['max_workers']}")
                print(f"  Max calls per minute: {config['max_calls_per_minute']}")
                print(f"  API key: {'Set' if config['api_key'] else 'Not set'}")
            elif user_input == 'analyze':
                print("Starting analysis...")
                try:
                    analyzer = LayoutBricksAnalyzer(
                        api_key=config['api_key'],
                        prompt_file=config['prompt_file'],
                        max_workers=config['max_workers'],
                        max_calls_per_minute=config['max_calls_per_minute']
                    )

                    results = analyzer.analyze_folder(config['input_folder'], config['output_folder'])

                    # Print summary
                    print(f"\n{'='*60}")
                    print("ANALYSIS SUMMARY")
                    print(f"{'='*60}")

                    if not results:
                        print("No new files were processed in this run.")
                        print("All files may have been processed already.")
                    else:
                        for result in results:
                            print(f"File: {result['file_path']}")
                            print(f"Score: {result['score']}/10")
                            print(f"Confidence: {result['confidence']}")
                            if result.get('error'):
                                print(f"Error: {result['error']}")
                            print("-" * 40)

                        # Statistics
                        scores = [r['score'] for r in results if not r.get('error')]
                        if scores:
                            avg_score = sum(scores) / len(scores)
                            high_scores = len([s for s in scores if s >= 7])
                            print(f"\nStatistics for this run:")
                            print(f"New files analyzed: {len(results)}")
                            print(f"Average score: {avg_score:.2f}")
                            print(f"Files with high scores (7+): {high_scores}")

                    print(f"\nResults saved to CSV and JSON in the output folder.")
                    print(f"CSV file: analysis_results.csv")
                    print(f"JSON file: analysis_summary.json")

                except Exception as e:
                    logger.error(f"Analysis failed: {e}")
                    print(f"Analysis failed: {e}")
            else:
                print("Unknown command. Available commands: 'analyze', 'check', 'status', 'stop'")

        except KeyboardInterrupt:
            print("\nProgram interrupted by user. Exiting...")
            break
        except Exception as e:
            logger.error(f"Error in interactive loop: {e}")
            print(f"Error: {e}")

    return 0


def main():
    """Main entry point for the application."""
    parser = argparse.ArgumentParser(description="Analyze text files for layout bricks instructions")
    parser.add_argument("--input_folder", default="input_ride", help="Path to the folder containing .txt files to analyze")
    parser.add_argument("--output-folder", default="output_ride", help="Path to the output folder (optional)")
    parser.add_argument("--api-key", help="OpenRouter API key (optional, can use OPENROUTER_API_KEY env var)")
    parser.add_argument("--prompt-file", default="_prompt.txt", help="Path to the prompt template file (default: _prompt.txt)")
    parser.add_argument("--max-workers", type=int, default=4, help="Maximum number of parallel workers (default: 4)")
    parser.add_argument("--max-calls-per-minute", type=int, default=18, help="Maximum API calls per minute (default: 18)")
    parser.add_argument("--batch", action="store_true", help="Run in batch mode (non-interactive)")

    args = parser.parse_args()

    # Check API calls at startup
    print("Checking remaining API calls...")
    api_key = args.api_key or get_api_key() if args.api_key else None
    try:
        if not api_key:
            api_key = get_api_key()
        result = check_api_calls_remaining(api_key)
        if result['success']:
            print(f"Remaining API calls: {result['remaining_calls']}")
        else:
            print(f"Failed to check API calls: {result['error']}")
    except Exception as e:
        print(f"Warning: Could not check API calls: {e}")

    print("Sleeping for 5 seconds...")
    time.sleep(5)

    # Choose mode based on arguments
    if args.batch:
        # Original batch mode
        try:
            # Initialize analyzer
            analyzer = LayoutBricksAnalyzer(
                api_key=api_key,
                prompt_file=args.prompt_file,
                max_workers=args.max_workers,
                max_calls_per_minute=args.max_calls_per_minute
            )

            # Run analysis
            results = analyzer.analyze_folder(args.input_folder, args.output_folder)

            # Print summary
            print(f"\n{'='*60}")
            print("ANALYSIS SUMMARY")
            print(f"{'='*60}")

            if not results:
                print("No new files were processed in this run.")
                print("All files may have been processed already.")
            else:
                for result in results:
                    print(f"File: {result['file_path']}")
                    print(f"Score: {result['score']}/10")
                    print(f"Confidence: {result['confidence']}")
                    if result.get('error'):
                        print(f"Error: {result['error']}")
                    print("-" * 40)

                # Statistics
                scores = [r['score'] for r in results if not r.get('error')]
                if scores:
                    avg_score = sum(scores) / len(scores)
                    high_scores = len([s for s in scores if s >= 7])
                    print(f"\nStatistics for this run:")
                    print(f"New files analyzed: {len(results)}")
                    print(f"Average score: {avg_score:.2f}")
                    print(f"Files with high scores (7+): {high_scores}")

            print(f"\nResults saved to CSV and JSON in the output folder.")
            print(f"CSV file: analysis_results.csv")
            print(f"JSON file: analysis_summary.json")

        except Exception as e:
            logger.error(f"Analysis failed: {e}")
            return 1
    else:
        # Interactive mode (default)
        return interactive_analysis_loop()

    return 0


if __name__ == "__main__":
    exit(main())
