"""
Data models for the Layout Bricks Instructions Analyzer.

This module contains Pydantic models used for structured data validation
and API response parsing.
"""

from typing import List
from pydantic import BaseModel


class AnalysisResult(BaseModel):
    """Pydantic model for structured AI response."""
    score: int
    reasoning: str
    keywords_found: List[str]
    confidence: str
    relevant_segments: List[str]
