"""
Core analysis functionality for the Layout Bricks Instructions Analyzer.

This module contains the main LayoutBricksAnalyzer class that orchestrates
the analysis of text files using LiteLLM and OpenRouter.
"""

import os
import json
import asyncio
import logging
import litellm
from pathlib import Path
from typing import Dict, List, Set

from models import AnalysisResult
from rate_limiter import RateLimiter
from file_utils import find_txt_files, load_processed_files, save_reasoning_file
from csv_handler import save_to_csv
from prompt_manager import PromptManager
from config import Config

logger = logging.getLogger(__name__)


class LayoutBricksAnalyzer:
    """Main analyzer class for processing text files."""

    def __init__(self, api_key: str = None, prompt_file: str = "_prompt.txt",
                 max_workers: int = 4, max_calls_per_minute: int = 18):
        """Initialize the analyzer with OpenRouter API key and prompt file."""
        self.config = Config()
        self.api_key = api_key or self.config.api_key

        # Initialize components
        self.prompt_manager = PromptManager(prompt_file)
        self.rate_limiter = RateLimiter(max_calls_per_minute)
        self.processed_files: Set[str] = set()
        self.max_workers = max_workers

    async def analyze_file(self, file_path: Path) -> Dict:
        """Analyze a single file for layout bricks instructions."""
        try:
            # Read file content
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            if not content.strip():
                return {
                    "file_path": str(file_path),
                    "score": 1,
                    "reasoning": "File is empty",
                    "keywords_found": [],
                    "confidence": "high",
                    "error": None
                }

            # Create prompt
            prompt = self.prompt_manager.create_analysis_prompt(content)

            # Wait for rate limiter permission
            await self.rate_limiter.acquire()

            # Call LiteLLM with OpenRouter using structured output
            response = await asyncio.to_thread(
                litellm.completion,
                model=self.config.model,
                messages=[
                    {"role": "system", "content": "You are a helpful assistant designed to output JSON for analyzing text content."},
                    {"role": "user", "content": prompt}
                ],
                response_format=AnalysisResult,
                temperature=self.config.temperature,
                max_tokens=self.config.max_tokens,
                api_key=self.api_key
            )

            # Parse structured response
            response_text = response.choices[0].message.content
            logger.debug(f"Structured response for {file_path}: {response_text}")

            # Parse the JSON response (should be valid JSON now)
            try:
                analysis_result = json.loads(response_text)

            except json.JSONDecodeError as e:
                logger.warning(f"Failed to parse structured JSON response for {file_path}: {e}")
                # Fallback: create basic result
                analysis_result = {
                    "score": 1,
                    "reasoning": f"Failed to parse structured response: {str(e)}. Raw response: {response_text[:200]}...",
                    "keywords_found": [],
                    "confidence": "low"
                }

            # Add file path and ensure score is valid
            analysis_result["file_path"] = str(file_path)
            analysis_result["score"] = max(1, min(10, int(analysis_result.get("score", 1))))
            analysis_result["error"] = None

            return analysis_result

        except Exception as e:
            logger.error(f"Error analyzing file {file_path}: {e}")
            return {
                "file_path": str(file_path),
                "score": 1,
                "reasoning": f"Error during analysis: {str(e)}",
                "keywords_found": [],
                "confidence": "low",
                "error": str(e)
            }

    async def process_file_worker(self, file_path: Path, output_path: Path,
                                semaphore: asyncio.Semaphore, csv_lock: asyncio.Lock) -> Dict:
        """Worker function to process a single file with concurrency control."""
        async with semaphore:
            try:
                result = await self.analyze_file(file_path)

                # Save reasoning file
                save_reasoning_file(result, output_path)

                # Save to CSV immediately (with lock to prevent concurrent writes)
                async with csv_lock:
                    save_to_csv([result], output_path)

                # Log result
                logger.info(f"File: {result['file_path']} | Score: {result['score']}/10 | Saved to CSV")

                return result

            except Exception as e:
                logger.error(f"Failed to analyze {file_path}: {e}")
                error_result = {
                    "file_path": str(file_path),
                    "score": 1,
                    "reasoning": f"Analysis failed: {str(e)}",
                    "keywords_found": [],
                    "confidence": "low",
                    "error": str(e)
                }

                # Save error result to CSV immediately
                async with csv_lock:
                    save_to_csv([error_result], output_path)

                return error_result

    async def analyze_folder_async(self, input_folder: str, output_folder: str = None) -> List[Dict]:
        """Async version of analyze_folder with parallel processing."""
        input_path = Path(input_folder)

        if not input_path.exists():
            raise FileNotFoundError(f"Input folder does not exist: {input_folder}")

        # Set up output folder
        if output_folder:
            output_path = Path(output_folder)
        else:
            output_path = Path("analysis_output")

        output_path.mkdir(parents=True, exist_ok=True)

        # Load already processed files
        self.processed_files = load_processed_files(output_path)

        # Find all txt files
        txt_files = find_txt_files(input_path)
        logger.info(f"Found {len(txt_files)} .txt files to analyze")

        # Filter out already processed files
        unprocessed_files = [f for f in txt_files if str(f) not in self.processed_files]

        if len(unprocessed_files) < len(txt_files):
            skipped_count = len(txt_files) - len(unprocessed_files)
            logger.info(f"Skipping {skipped_count} already processed files")

        if not unprocessed_files:
            if not txt_files:
                logger.warning("No .txt files found in the specified folder")
            else:
                logger.info("All files have already been processed")
            return []

        logger.info(f"Processing {len(unprocessed_files)} new files with {self.max_workers} workers")

        # Create semaphore to limit concurrent workers and lock for CSV writing
        semaphore = asyncio.Semaphore(self.max_workers)
        csv_lock = asyncio.Lock()

        # Create tasks for all files
        tasks = [
            self.process_file_worker(file_path, output_path, semaphore, csv_lock)
            for file_path in unprocessed_files
        ]

        # Process all files concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Filter out exceptions and convert them to error results
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Task failed for {unprocessed_files[i]}: {result}")
                processed_results.append({
                    "file_path": str(unprocessed_files[i]),
                    "score": 1,
                    "reasoning": f"Task failed: {str(result)}",
                    "keywords_found": [],
                    "confidence": "low",
                    "error": str(result)
                })
            else:
                processed_results.append(result)

        # Save results if we have any new ones
        if processed_results:
            # Save summary results (JSON) - CSV is already saved per file
            summary_path = output_path / "analysis_summary.json"

            # Load existing results if file exists
            all_results = []
            if summary_path.exists():
                try:
                    with open(summary_path, 'r', encoding='utf-8') as f:
                        all_results = json.load(f)
                except Exception as e:
                    logger.warning(f"Could not load existing summary: {e}")

            # Add new results
            all_results.extend(processed_results)

            # Save updated summary
            with open(summary_path, 'w', encoding='utf-8') as f:
                json.dump(all_results, f, indent=2, ensure_ascii=False)

        logger.info(f"Analysis complete. Results saved to: {output_path}")
        return processed_results

    def analyze_folder(self, input_folder: str, output_folder: str = None) -> List[Dict]:
        """Analyze all txt files in the specified folder (synchronous wrapper for async method)."""
        return asyncio.run(self.analyze_folder_async(input_folder, output_folder))
