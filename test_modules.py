#!/usr/bin/env python3
"""
Simple test script to verify the modular structure works correctly.
This script tests basic imports and functionality without requiring API keys.
"""

def test_imports():
    """Test that all modules can be imported successfully."""
    print("Testing module imports...")
    
    try:
        from models import AnalysisResult
        print("✓ models.py imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import models.py: {e}")
        return False
    
    try:
        from rate_limiter import RateLimiter
        print("✓ rate_limiter.py imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import rate_limiter.py: {e}")
        return False
    
    try:
        from file_utils import find_txt_files, load_processed_files, save_reasoning_file
        print("✓ file_utils.py imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import file_utils.py: {e}")
        return False
    
    try:
        from csv_handler import save_to_csv
        print("✓ csv_handler.py imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import csv_handler.py: {e}")
        return False
    
    try:
        from prompt_manager import PromptManager
        print("✓ prompt_manager.py imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import prompt_manager.py: {e}")
        return False
    
    try:
        from config import setup_logging, Config
        print("✓ config.py imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import config.py: {e}")
        return False
    
    try:
        # This might fail if API key is not set, but import should work
        from analyzer import LayoutBricksAnalyzer
        print("✓ analyzer.py imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import analyzer.py: {e}")
        return False
    
    try:
        from main import main
        print("✓ main.py imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import main.py: {e}")
        return False
    
    return True


def test_basic_functionality():
    """Test basic functionality of key components."""
    print("\nTesting basic functionality...")
    
    # Test AnalysisResult model
    try:
        from models import AnalysisResult
        result = AnalysisResult(
            score=8,
            reasoning="Test reasoning",
            keywords_found=["test", "keyword"],
            confidence="high"
        )
        print("✓ AnalysisResult model works correctly")
    except Exception as e:
        print(f"✗ AnalysisResult model failed: {e}")
        return False
    
    # Test Config class
    try:
        from config import Config
        config = Config()
        assert config.model == "openrouter/deepseek/deepseek-r1-0528:free"
        assert config.default_max_workers == 4
        print("✓ Config class works correctly")
    except Exception as e:
        print(f"✗ Config class failed: {e}")
        return False
    
    # Test RateLimiter (basic instantiation)
    try:
        from rate_limiter import RateLimiter
        limiter = RateLimiter(max_calls_per_minute=10)
        assert limiter.max_calls_per_minute == 10
        print("✓ RateLimiter instantiation works correctly")
    except Exception as e:
        print(f"✗ RateLimiter failed: {e}")
        return False
    
    return True


def test_backward_compatibility():
    """Test that backward compatibility wrapper works."""
    print("\nTesting backward compatibility...")
    
    try:
        # Test that analyze.py can import from new modules
        from analyze import AnalysisResult, RateLimiter, LayoutBricksAnalyzer, main
        print("✓ Backward compatibility imports work correctly")
    except ImportError as e:
        print(f"✗ Backward compatibility failed: {e}")
        return False
    
    return True


def main():
    """Run all tests."""
    print("=" * 60)
    print("MODULAR STRUCTURE TEST SUITE")
    print("=" * 60)
    
    all_passed = True
    
    # Run tests
    all_passed &= test_imports()
    all_passed &= test_basic_functionality()
    all_passed &= test_backward_compatibility()
    
    # Summary
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 ALL TESTS PASSED! Modular structure is working correctly.")
        print("You can now use either:")
        print("  - python main.py (recommended for new projects)")
        print("  - python analyze.py (backward compatibility)")
    else:
        print("❌ SOME TESTS FAILED! Please check the error messages above.")
    print("=" * 60)
    
    return 0 if all_passed else 1


if __name__ == "__main__":
    exit(main())
